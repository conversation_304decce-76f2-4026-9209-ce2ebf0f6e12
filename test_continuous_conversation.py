#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连续追问功能测试脚本
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入核心模块
try:
    from core.utils.context_manager import ContextManager, ConversationRound
    from core.utils.performance_monitor import PerformanceMonitor, monitor_performance
    print("✅ 核心模块导入成功")
except ImportError as e:
    print(f"❌ 核心模块导入失败: {e}")
    sys.exit(1)


class ContinuousConversationTester:
    """连续追问功能测试器"""

    def __init__(self):
        """初始化测试器"""
        try:
            self.context_manager = ContextManager(enable_logging=False)  # 禁用日志避免依赖问题
            self.performance_monitor = PerformanceMonitor(enable_logging=False)
            self.test_results = []
            print("✅ 测试器初始化成功")
        except Exception as e:
            print(f"❌ 测试器初始化失败: {e}")
            raise
    
    def test_context_management(self):
        """测试上下文管理功能"""
        print("🧪 测试上下文管理功能...")
        
        # 测试数据
        test_conversations = [
            ("请分析销售数据的趋势", "我来为您分析销售数据的趋势", "df.plot()"),
            ("刚才的图表能否加上标题", "我来为之前的图表添加标题", "plt.title('销售趋势')"),
            ("基于上面的分析，预测下个月的销售额", "基于之前的趋势分析，我来预测下个月销售额", "forecast_model.predict()"),
            ("能否用不同的颜色显示", "我来调整图表颜色", "plt.plot(color='red')"),
            ("总结一下我们的分析结果", "让我总结一下我们的分析", "print('分析总结')")
        ]
        
        # 添加对话轮次
        for i, (user_msg, assistant_msg, code) in enumerate(test_conversations, 1):
            print(f"  添加第 {i} 轮对话...")
            
            should_summarize = self.context_manager.add_conversation_round(
                user_message=user_msg,
                assistant_message=assistant_msg,
                code=code,
                execution_result={"success": True, "output": "执行成功"}
            )
            
            if should_summarize:
                print(f"  ✅ 第 {i} 轮触发摘要生成")
                summary = self.context_manager.generate_summary()
                if summary:
                    print(f"  📝 摘要: {summary.summary_text}")
        
        # 测试引用检测
        print("\n🔍 测试引用检测...")
        test_instructions = [
            "修改之前的图表",
            "基于刚才的分析",
            "调整上面的代码",
            "重新生成图表"
        ]
        
        for instruction in test_instructions:
            context = self.context_manager.build_context_for_llm(instruction)
            references = context.get('references', {})
            print(f"  指令: '{instruction}' -> 引用: {list(references.keys())}")
        
        # 获取统计信息
        stats = self.context_manager.get_context_stats()
        print(f"\n📊 上下文统计:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        return True
    
    @monitor_performance(context_size=1000, token_count=500)
    def test_performance_monitoring(self):
        """测试性能监控功能"""
        print("\n⚡ 测试性能监控功能...")
        
        # 模拟一些耗时操作
        import time
        time.sleep(0.1)  # 模拟处理时间
        
        # 获取性能摘要
        summary = self.performance_monitor.get_performance_summary()
        print(f"📈 性能摘要:")
        for key, value in summary.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.3f}")
            else:
                print(f"  {key}: {value}")
        
        return True
    
    def test_context_optimization(self):
        """测试上下文优化功能"""
        print("\n🔧 测试上下文优化功能...")
        
        # 添加大量对话轮次
        for i in range(15):
            self.context_manager.add_conversation_round(
                user_message=f"测试消息 {i+1}",
                assistant_message=f"测试回复 {i+1}",
                code=f"print('test {i+1}')",
                execution_result={"success": True}
            )
        
        print(f"  添加了 15 轮对话")
        
        # 检查内存使用
        stats_before = self.context_manager.get_context_stats()
        print(f"  清理前: {stats_before['memory_usage_estimate']}")
        
        # 执行清理
        self.context_manager.cleanup_old_data(keep_recent_rounds=5)
        
        # 检查清理后的状态
        stats_after = self.context_manager.get_context_stats()
        print(f"  清理后: {stats_after['memory_usage_estimate']}")
        print(f"  保留轮次: {stats_after['total_rounds']}")
        
        return True
    
    def test_summary_generation(self):
        """测试摘要生成功能"""
        print("\n📝 测试摘要生成功能...")
        
        # 重置上下文
        self.context_manager.reset_context()
        
        # 添加具有明确业务场景的对话
        business_conversations = [
            ("我需要分析公司的销售数据", "好的，我来帮您分析销售数据", "df = pd.read_csv('sales.csv')"),
            ("请显示各产品的销售额排名", "我来为您生成销售额排名", "df.groupby('product').sum().sort_values()"),
            ("能否用柱状图展示", "我来生成柱状图", "df.plot(kind='bar')"),
            ("这个图表需要优化颜色和标题", "我来优化图表的显示效果", "plt.title('产品销售排名'); plt.colormap('viridis')"),
        ]
        
        for user_msg, assistant_msg, code in business_conversations:
            self.context_manager.add_conversation_round(
                user_message=user_msg,
                assistant_message=assistant_msg,
                code=code,
                execution_result={"success": True}
            )
        
        # 生成摘要
        summary = self.context_manager.generate_summary()
        if summary:
            print(f"  ✅ 摘要生成成功")
            print(f"  用户身份: {summary.user_context}")
            print(f"  核心目标: {summary.core_objective}")
            print(f"  关注点: {', '.join(summary.key_concerns)}")
            print(f"  当前进展: {summary.current_progress}")
            print(f"  摘要文本: {summary.summary_text}")
        else:
            print(f"  ❌ 摘要生成失败")
            return False
        
        return True
    
    def test_reference_tracking(self):
        """测试引用跟踪功能"""
        print("\n🔗 测试引用跟踪功能...")
        
        # 重置上下文
        self.context_manager.reset_context()
        
        # 添加包含不同类型输出的对话
        self.context_manager.add_conversation_round(
            user_message="生成一个销售趋势图",
            assistant_message="我来生成销售趋势图",
            code="plt.plot(df['date'], df['sales']); plt.show()",
            execution_result={"success": True}
        )
        
        self.context_manager.add_conversation_round(
            user_message="创建一个数据汇总表",
            assistant_message="我来创建数据汇总表",
            code="summary_df = df.groupby('category').agg({'sales': 'sum'})",
            execution_result={"success": True}
        )
        
        # 测试引用检测
        test_cases = [
            ("修改之前的图表颜色", ["chart"]),
            ("基于刚才的表格进行分析", ["table"]),
            ("结合上面的图表和表格", ["chart", "table"]),
            ("重新生成数据", [])
        ]
        
        for instruction, expected_refs in test_cases:
            context = self.context_manager.build_context_for_llm(instruction)
            actual_refs = list(context.get('references', {}).keys())
            
            success = set(expected_refs) == set(actual_refs)
            status = "✅" if success else "❌"
            print(f"  {status} '{instruction}' -> 期望: {expected_refs}, 实际: {actual_refs}")
        
        return True
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始连续追问功能测试\n")
        print("=" * 60)
        
        tests = [
            ("上下文管理", self.test_context_management),
            ("性能监控", self.test_performance_monitoring),
            ("上下文优化", self.test_context_optimization),
            ("摘要生成", self.test_summary_generation),
            ("引用跟踪", self.test_reference_tracking),
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                print(f"\n{'='*20} {test_name} {'='*20}")
                success = test_func()
                results.append((test_name, success))
                status = "✅ 通过" if success else "❌ 失败"
                print(f"\n{test_name}: {status}")
            except Exception as e:
                print(f"\n❌ {test_name} 测试异常: {str(e)}")
                results.append((test_name, False))
        
        # 输出测试总结
        print(f"\n{'='*60}")
        print("📊 测试总结:")
        
        passed = sum(1 for _, success in results if success)
        total = len(results)
        
        for test_name, success in results:
            status = "✅" if success else "❌"
            print(f"  {status} {test_name}")
        
        print(f"\n通过率: {passed}/{total} ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🎉 所有测试通过！连续追问功能准备就绪。")
        else:
            print("⚠️  部分测试失败，请检查相关功能。")
        
        return passed == total


def main():
    """主函数"""
    print("🤖 AI数据分析平台 - 连续追问功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建测试器
    tester = ContinuousConversationTester()
    
    # 运行测试
    success = tester.run_all_tests()
    
    # 退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
