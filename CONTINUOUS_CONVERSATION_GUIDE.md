# 🧠 连续追问功能使用指南

## 📋 功能概述

AI数据分析平台现已支持智能的连续追问功能，基于"定期摘要法"实现上下文感知的对话体验。

### ✨ 核心特性

- **🔄 上下文连贯性**: AI能理解对话历史，支持"基于刚才的分析"等自然表达
- **📝 智能摘要**: 每3-5轮对话自动生成摘要，优化性能
- **🔗 引用跟踪**: 自动跟踪图表、表格等分析结果，支持引用修改
- **⚡ 性能优化**: 智能的上下文管理，确保响应速度
- **📊 可视化管理**: 侧边栏提供上下文状态监控和管理

---

## 🚀 快速开始

### 1. 启动应用
```bash
python streamlit_app.py
```

### 2. 基础对话流程
1. **上传数据**: 在侧边栏上传CSV/Excel文件
2. **开始分析**: 输入分析需求，如"分析销售趋势"
3. **连续追问**: 使用自然语言继续对话
   - "基于刚才的分析，预测下个月的数据"
   - "修改之前的图表颜色"
   - "结合上面的结果，生成报告"

### 3. 查看上下文状态
在侧边栏的"🧠 上下文管理"区域可以看到：
- 当前对话轮次
- 距离下次摘要的轮数
- 内存使用情况
- 摘要状态

---

## 💡 使用技巧

### 自然语言引用
支持以下引用方式：

| 引用词汇 | 示例 | 说明 |
|---------|------|------|
| 之前的 | "修改之前的图表" | 引用最近生成的图表 |
| 刚才的 | "基于刚才的分析" | 引用最近的分析结果 |
| 上面的 | "调整上面的代码" | 引用最近的代码 |
| 这个 | "这个图表需要优化" | 引用当前显示的内容 |

### 连续分析示例

**第1轮**: "请分析各产品的销售额"
```python
# AI生成代码
product_sales = df.groupby('产品名称')['销售额'].sum()
st.bar_chart(product_sales)
```

**第2轮**: "基于刚才的分析，找出销售额最高的3个产品"
```python
# AI理解上下文，基于之前的分析继续
top_3_products = product_sales.nlargest(3)
st.write("销售额最高的3个产品：")
st.dataframe(top_3_products)
```

**第3轮**: "为之前的柱状图添加标题和颜色"
```python
# AI引用之前的图表代码并优化
import matplotlib.pyplot as plt
fig, ax = plt.subplots()
product_sales.plot(kind='bar', ax=ax, color='skyblue')
ax.set_title('各产品销售额分析')
ax.set_xlabel('产品名称')
ax.set_ylabel('销售额')
st.pyplot(fig)
```

---

## 🔧 高级功能

### 上下文管理

#### 自动摘要
- **触发条件**: 每3-5轮对话
- **摘要内容**: 用户场景、核心目标、关键关注点、当前进展
- **优化效果**: 减少Token消耗，提升响应速度

#### 手动操作
在侧边栏可以进行：
- **🧹 清理上下文**: 保留最近5轮对话，清理旧数据
- **🔄 重置上下文**: 完全重置，开始新的对话会话
- **📝 生成摘要**: 手动触发摘要生成

### 性能监控

#### 实时指标
- **响应时间**: 每次分析的耗时
- **内存使用**: 当前内存占用情况
- **上下文大小**: 当前上下文的数据量
- **Token消耗**: LLM调用的Token使用量

#### 调试模式
启用"🔍 显示调试信息"可以看到：
- 详细的上下文统计
- 性能指标
- 引用检测结果

---

## 📊 最佳实践

### 1. 对话规划
- **明确目标**: 开始时说明分析目标
- **逐步深入**: 从基础分析到深度洞察
- **适时总结**: 每隔几轮总结当前发现

### 2. 引用使用
- **具体引用**: "修改刚才的柱状图"比"修改图表"更准确
- **时序清晰**: 按照对话顺序进行引用
- **避免歧义**: 如有多个图表，明确指出是哪一个

### 3. 性能优化
- **定期清理**: 长对话时使用"清理上下文"功能
- **重置会话**: 切换分析主题时重置上下文
- **监控指标**: 关注响应时间和内存使用

---

## 🐛 故障排除

### 常见问题

#### 1. 引用失效
**现象**: AI无法理解"之前的图表"等引用
**解决**: 
- 检查引用的内容是否在最近3轮对话中
- 使用更具体的描述
- 手动生成摘要

#### 2. 响应变慢
**现象**: AI回复时间明显增长
**解决**:
- 查看上下文管理中的内存使用
- 执行"清理上下文"操作
- 检查是否需要重置对话

#### 3. 上下文丢失
**现象**: AI忘记了之前的对话内容
**解决**:
- 检查是否触发了自动摘要
- 查看摘要内容是否准确
- 必要时手动重述关键信息

### 调试步骤

1. **启用调试模式**: 勾选"显示调试信息"
2. **查看上下文统计**: 检查轮次和摘要状态
3. **检查性能指标**: 确认响应时间和内存使用
4. **导出诊断数据**: 使用导出功能获取详细信息

---

## 📈 性能指标

### 正常范围
- **响应时间**: < 10秒
- **内存使用**: < 80%
- **上下文大小**: < 8000字符
- **对话轮次**: 建议每10轮清理一次

### 告警阈值
- **响应时间**: > 15秒
- **内存使用**: > 80%
- **上下文大小**: > 10000字符

---

## 🔄 更新日志

### v2.1.0 (当前版本)
- ✅ 实现定期摘要法
- ✅ 添加引用跟踪功能
- ✅ 集成性能监控
- ✅ 优化上下文管理界面

### 计划功能
- 🔄 多主题并行对话
- 🔄 对话历史搜索
- 🔄 智能建议功能
- 🔄 导出对话报告

---

## 📞 技术支持

### 测试验证
运行测试脚本验证功能：
```bash
python test_continuous_conversation.py
```

### 日志查看
```bash
# 查看应用日志
tail -f logs/app_*.log

# 查看LLM日志
tail -f logs/llm_*.log
```

### 问题反馈
如遇到问题，请提供：
1. 具体的对话内容
2. 错误信息截图
3. 上下文调试信息
4. 系统环境信息

---

**🎉 享受智能的连续对话体验！**
