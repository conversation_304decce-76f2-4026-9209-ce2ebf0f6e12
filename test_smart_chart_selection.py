#!/usr/bin/env python3
"""
测试智能图表选择功能
验证修复后是否只生成一种合适的图表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.processors.chart_fixer import ChartFixer

def test_smart_chart_selection():
    """测试智能图表选择功能"""
    print("🎯 测试智能图表选择功能")
    print("=" * 80)
    
    fixer = ChartFixer()
    
    # 测试1：时间趋势查询（应该只保留折线图）
    print("\n📈 测试1：时间趋势查询")
    print("查询：请分析2024年1月每天的销售额趋势")
    
    multi_chart_code1 = """
import pandas as pd
import matplotlib.pyplot as plt

# 分析每天销售额趋势
daily_sales = df.groupby('日期')['销售额'].sum()

# 生成折线图
plt.plot(daily_sales.index, daily_sales.values)
plt.title('每天销售额趋势')
plt.show()

# 也生成柱状图对比
plt.bar(daily_sales.index, daily_sales.values)
plt.title('每天销售额柱状图')
plt.show()
"""
    
    print("原始代码（包含多个图表）:")
    print(multi_chart_code1)
    
    fixed_code1 = fixer.fix_charts(multi_chart_code1, "请分析2024年1月每天的销售额趋势")
    
    print("\n修复后代码:")
    print(fixed_code1)
    
    # 验证结果
    bar_count = fixed_code1.count('st.bar_chart')
    line_count = fixed_code1.count('st.line_chart')
    
    print(f"\n📊 图表统计:")
    print(f"  柱状图: {bar_count}")
    print(f"  折线图: {line_count}")
    
    if line_count > 0 and bar_count == 0:
        print("  ✅ 成功：只保留了折线图")
    else:
        print("  ❌ 失败：仍有多个图表类型")
    
    # 测试2：占比分析查询（应该只保留饼图）
    print("\n" + "=" * 80)
    print("\n🥧 测试2：占比分析查询")
    print("查询：请分析各业务人员销售额占比")
    
    multi_chart_code2 = """
import pandas as pd
import matplotlib.pyplot as plt

# 分析各业务人员销售额占比
staff_sales = df.groupby('业务人员')['销售额'].sum()

# 生成饼图
plt.pie(staff_sales.values, labels=staff_sales.index)
plt.title('各业务人员销售额占比')
plt.show()

# 也生成折线图
plt.plot(staff_sales.index, staff_sales.values)
plt.title('业务人员销售额趋势')
plt.show()
"""
    
    print("原始代码（包含多个图表）:")
    print(multi_chart_code2)
    
    fixed_code2 = fixer.fix_charts(multi_chart_code2, "请分析各业务人员销售额占比")
    
    print("\n修复后代码:")
    print(fixed_code2)
    
    # 验证结果
    pie_count = fixed_code2.count('px.pie')
    line_count2 = fixed_code2.count('st.line_chart')
    bar_count2 = fixed_code2.count('st.bar_chart')
    
    print(f"\n📊 图表统计:")
    print(f"  饼图: {pie_count}")
    print(f"  折线图: {line_count2}")
    print(f"  柱状图: {bar_count2}")
    
    if pie_count > 0 and line_count2 == 0 and bar_count2 == 0:
        print("  ✅ 成功：只保留了饼图")
    else:
        print("  ❌ 失败：仍有多个图表类型")
    
    # 测试3：对比分析查询（应该只保留柱状图）
    print("\n" + "=" * 80)
    print("\n📊 测试3：对比分析查询")
    print("查询：对比各产品的销售额")
    
    multi_chart_code3 = """
import pandas as pd
import matplotlib.pyplot as plt

# 对比各产品销售额
product_sales = df.groupby('产品名称')['销售额'].sum()

# 生成柱状图
plt.bar(product_sales.index, product_sales.values)
plt.title('各产品销售额对比')
plt.show()

# 也生成散点图
plt.scatter(range(len(product_sales)), product_sales.values)
plt.title('产品销售额散点图')
plt.show()
"""
    
    print("原始代码（包含多个图表）:")
    print(multi_chart_code3)
    
    fixed_code3 = fixer.fix_charts(multi_chart_code3, "对比各产品的销售额")
    
    print("\n修复后代码:")
    print(fixed_code3)
    
    # 验证结果
    bar_count3 = fixed_code3.count('st.bar_chart')
    scatter_count3 = fixed_code3.count('st.scatter_chart')
    
    print(f"\n📊 图表统计:")
    print(f"  柱状图: {bar_count3}")
    print(f"  散点图: {scatter_count3}")
    
    if bar_count3 > 0 and scatter_count3 == 0:
        print("  ✅ 成功：只保留了柱状图")
    else:
        print("  ❌ 失败：仍有多个图表类型")

def test_intent_detection():
    """测试意图检测功能"""
    print("\n" + "=" * 80)
    print("🧠 测试意图检测功能")
    print("=" * 80)
    
    fixer = ChartFixer()
    
    test_cases = [
        ("请分析2024年1月每天的销售额趋势", "trend"),
        ("分析各业务人员销售额占比", "proportion"),
        ("对比各产品的销售额", "comparison"),
        ("分析销售额与销量的相关性", "correlation"),
        ("计算总销售额", "none"),
    ]
    
    for instruction, expected in test_cases:
        detected = fixer._detect_user_intent(instruction)
        status = "✅" if detected == expected else "❌"
        print(f"{status} '{instruction}' → {detected} (期望: {expected})")

if __name__ == "__main__":
    test_smart_chart_selection()
    test_intent_detection()
    
    print("\n" + "=" * 80)
    print("🎉 智能图表选择测试完成")
    print("如果所有测试都显示✅，说明修复成功")
    print("现在应该只会生成一种最合适的图表类型")
    print("=" * 80)
