#!/usr/bin/env python3
"""
测试多个图表同时出现的问题
分析为什么柱状图总是出现
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.processors.chart_fixer import ChartFixer

def test_chart_conversion_issue():
    """测试图表转换问题"""
    print("🔍 分析多个图表同时出现的问题")
    print("=" * 80)
    
    fixer = ChartFixer()
    
    # 测试1：时间趋势查询（应该只生成折线图）
    print("\n📈 测试1：时间趋势查询")
    print("查询：请分析2024年1月每天的销售额趋势")
    
    # 模拟LLM可能生成的代码（包含多种图表）
    llm_generated_code1 = """
import pandas as pd
import matplotlib.pyplot as plt

# 分析每天销售额趋势
daily_sales = df.groupby('日期')['销售额'].sum()

# 生成折线图
plt.plot(daily_sales.index, daily_sales.values)
plt.title('每天销售额趋势')
plt.show()

# 也生成柱状图对比
plt.bar(daily_sales.index, daily_sales.values)
plt.title('每天销售额柱状图')
plt.show()
"""
    
    print("LLM生成的原始代码:")
    print(llm_generated_code1)
    
    fixed_code1 = fixer.fix_charts(llm_generated_code1, "请分析2024年1月每天的销售额趋势")
    
    print("\n修复后的代码:")
    print(fixed_code1)
    
    # 分析问题
    bar_chart_count = fixed_code1.count('st.bar_chart')
    line_chart_count = fixed_code1.count('st.line_chart')
    
    print(f"\n📊 图表统计:")
    print(f"  柱状图数量: {bar_chart_count}")
    print(f"  折线图数量: {line_chart_count}")
    
    if bar_chart_count > 0 and line_chart_count > 0:
        print("  ❌ 问题确认：同时出现了柱状图和折线图")
    
    # 测试2：占比分析查询（应该只生成饼图）
    print("\n" + "=" * 80)
    print("\n🥧 测试2：占比分析查询")
    print("查询：请分析各业务人员销售额占比")
    
    llm_generated_code2 = """
import pandas as pd
import matplotlib.pyplot as plt

# 分析各业务人员销售额占比
staff_sales = df.groupby('业务人员')['销售额'].sum()

# 生成饼图
plt.pie(staff_sales.values, labels=staff_sales.index)
plt.title('各业务人员销售额占比')
plt.show()

# 也生成折线图
plt.plot(staff_sales.index, staff_sales.values)
plt.title('业务人员销售额趋势')
plt.show()
"""
    
    print("LLM生成的原始代码:")
    print(llm_generated_code2)
    
    fixed_code2 = fixer.fix_charts(llm_generated_code2, "请分析各业务人员销售额占比")
    
    print("\n修复后的代码:")
    print(fixed_code2)
    
    # 分析问题
    pie_chart_count = fixed_code2.count('px.pie')
    line_chart_count2 = fixed_code2.count('st.line_chart')
    bar_chart_count2 = fixed_code2.count('st.bar_chart')
    
    print(f"\n📊 图表统计:")
    print(f"  饼图数量: {pie_chart_count}")
    print(f"  折线图数量: {line_chart_count2}")
    print(f"  柱状图数量: {bar_chart_count2}")
    
    if (pie_chart_count > 0 and line_chart_count2 > 0) or (pie_chart_count > 0 and bar_chart_count2 > 0):
        print("  ❌ 问题确认：饼图与其他图表同时出现")

def analyze_chart_fixer_logic():
    """分析图表修复器的逻辑问题"""
    print("\n" + "=" * 80)
    print("🔧 分析图表修复器逻辑")
    print("=" * 80)
    
    fixer = ChartFixer()
    
    # 检查转换规则
    print("📋 matplotlib转换规则:")
    matplotlib_patterns = [
        ('plt.bar()', '_convert_to_st_bar_chart'),
        ('plt.plot()', '_convert_to_st_line_chart'),
        ('plt.scatter()', '_convert_to_st_scatter_chart'),
        ('plt.hist()', '_convert_to_st_bar_chart'),
        ('plt.pie()', '_convert_to_plotly_pie_chart'),
    ]
    
    for pattern, converter in matplotlib_patterns:
        print(f"  {pattern} → {converter}")
    
    print("\n📋 plotly转换规则:")
    plotly_patterns = [
        ('px.bar()', '_convert_px_bar_to_st'),
        ('px.line()', '_convert_px_line_to_st'),
        ('px.scatter()', '_convert_px_scatter_to_st'),
        ('px.pie()', '保持不变'),
    ]
    
    for pattern, converter in plotly_patterns:
        print(f"  {pattern} → {converter}")
    
    # 测试单个转换方法
    print("\n🔍 测试单个转换方法:")
    
    # 测试柱状图转换
    bar_result = fixer._convert_to_st_bar_chart("plt.bar(x, y)")
    print(f"\n柱状图转换结果包含的图表:")
    if 'st.bar_chart' in bar_result:
        print("  ✓ st.bar_chart")
    
    # 测试折线图转换
    line_result = fixer._convert_to_st_line_chart("plt.plot(x, y)")
    print(f"\n折线图转换结果包含的图表:")
    if 'st.line_chart' in line_result:
        print("  ✓ st.line_chart")
    
    # 测试饼图转换
    pie_result = fixer._convert_to_plotly_pie_chart("plt.pie(values, labels)")
    print(f"\n饼图转换结果包含的图表:")
    if 'px.pie' in pie_result:
        print("  ✓ px.pie")
    if 'st.plotly_chart' in pie_result:
        print("  ✓ st.plotly_chart")

def identify_root_cause():
    """识别根本原因"""
    print("\n" + "=" * 80)
    print("🎯 根本原因分析")
    print("=" * 80)
    
    print("可能的原因:")
    print("1. ❌ LLM生成了多个图表代码")
    print("   - LLM可能同时生成plt.plot()和plt.bar()")
    print("   - 图表修复器会将每个都转换为对应的Streamlit图表")
    
    print("\n2. ❌ 图表修复器的转换逻辑问题")
    print("   - 每个matplotlib函数都被独立转换")
    print("   - 没有去重或优先级处理")
    
    print("\n3. ❌ 缺少图表类型优先级机制")
    print("   - 没有根据用户意图选择最合适的图表")
    print("   - 所有检测到的图表都被保留")
    
    print("\n4. ❌ 图表保存功能可能添加额外代码")
    print("   - _add_chart_saving可能添加额外的图表相关代码")
    
    print("\n🔧 解决方案建议:")
    print("1. ✅ 添加图表类型优先级机制")
    print("2. ✅ 根据用户查询意图选择最合适的图表")
    print("3. ✅ 去除重复或不相关的图表")
    print("4. ✅ 改进LLM提示词，避免生成多个图表")

if __name__ == "__main__":
    test_chart_conversion_issue()
    analyze_chart_fixer_logic()
    identify_root_cause()
