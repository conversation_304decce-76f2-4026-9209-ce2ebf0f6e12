#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版连续追问功能测试脚本
避免复杂依赖，专注核心功能测试
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("🧪 测试模块导入...")
    
    try:
        from core.utils.context_manager import ContextManager, ConversationRound, ConversationSummary
        print("  ✅ ContextManager 导入成功")
        return True, ContextManager
    except ImportError as e:
        print(f"  ❌ ContextManager 导入失败: {e}")
        return False, None

def test_context_manager_basic(ContextManager):
    """测试上下文管理器基础功能"""
    print("\n🧪 测试上下文管理器基础功能...")
    
    try:
        # 创建模拟的session state
        import streamlit as st
        if 'context_manager' not in st.session_state:
            st.session_state.context_manager = {
                'conversation_rounds': [],
                'current_summary': None,
                'round_counter': 0,
                'last_summary_round': 0,
                'topic_changed': False,
                'reference_tracker': {
                    'last_chart': None,
                    'last_table': None,
                    'last_analysis': None,
                    'variables': {}
                }
            }
        
        # 创建上下文管理器
        context_manager = ContextManager(enable_logging=False)
        print("  ✅ 上下文管理器创建成功")
        
        # 测试添加对话轮次
        should_summarize = context_manager.add_conversation_round(
            user_message="请分析销售数据",
            assistant_message="我来分析销售数据",
            code="df.describe()",
            execution_result={"success": True}
        )
        print(f"  ✅ 添加对话轮次成功，需要摘要: {should_summarize}")
        
        # 测试统计信息
        stats = context_manager.get_context_stats()
        print(f"  ✅ 获取统计信息成功: {stats}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 上下文管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_context_manager_without_streamlit():
    """不依赖Streamlit的上下文管理器测试"""
    print("\n🧪 测试独立上下文管理器...")
    
    try:
        # 模拟session state
        class MockSessionState:
            def __init__(self):
                self.context_manager = {
                    'conversation_rounds': [],
                    'current_summary': None,
                    'round_counter': 0,
                    'last_summary_round': 0,
                    'topic_changed': False,
                    'reference_tracker': {
                        'last_chart': None,
                        'last_table': None,
                        'last_analysis': None,
                        'variables': {}
                    }
                }
        
        # 创建模拟的streamlit模块
        class MockStreamlit:
            def __init__(self):
                self.session_state = MockSessionState()
        
        # 替换streamlit导入
        sys.modules['streamlit'] = MockStreamlit()
        
        # 重新导入上下文管理器
        from core.utils.context_manager import ContextManager
        
        # 创建上下文管理器
        context_manager = ContextManager(enable_logging=False)
        print("  ✅ 独立上下文管理器创建成功")
        
        # 测试对话轮次
        test_conversations = [
            ("分析销售趋势", "我来分析销售趋势", "df.plot()"),
            ("修改图表颜色", "我来修改图表颜色", "plt.plot(color='red')"),
            ("生成报告", "我来生成报告", "print('报告')"),
            ("总结分析", "我来总结分析", "summary = df.describe()"),
        ]
        
        for i, (user_msg, assistant_msg, code) in enumerate(test_conversations, 1):
            should_summarize = context_manager.add_conversation_round(
                user_message=user_msg,
                assistant_message=assistant_msg,
                code=code,
                execution_result={"success": True}
            )
            print(f"  第{i}轮对话添加成功，需要摘要: {should_summarize}")
            
            if should_summarize:
                summary = context_manager.generate_summary()
                if summary:
                    print(f"  📝 摘要生成成功: {summary.summary_text[:50]}...")
                else:
                    print("  ⚠️ 摘要生成失败")
        
        # 测试引用检测
        print("\n  🔍 测试引用检测...")
        test_instructions = [
            "修改之前的图表",
            "基于刚才的分析",
            "调整上面的代码"
        ]
        
        for instruction in test_instructions:
            context = context_manager.build_context_for_llm(instruction)
            references = context.get('references', {})
            print(f"    '{instruction}' -> 引用: {list(references.keys())}")
        
        # 测试统计信息
        stats = context_manager.get_context_stats()
        print(f"  📊 统计信息: 总轮次={stats['total_rounds']}, 内存={stats['memory_usage_estimate']}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 独立上下文管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_conversation_summary():
    """测试对话摘要功能"""
    print("\n🧪 测试对话摘要功能...")
    
    try:
        from core.utils.context_manager import ConversationSummary
        
        # 创建测试摘要
        summary = ConversationSummary(
            summary_text="用户正在进行销售数据分析",
            key_points=["分析趋势", "生成图表"],
            user_context="数据分析师",
            core_objective="销售分析",
            key_concerns=["数据质量", "可视化效果"],
            current_progress="进展顺利",
            rounds_covered=3
        )
        
        print(f"  ✅ 摘要创建成功: {summary.summary_text}")
        print(f"  用户身份: {summary.user_context}")
        print(f"  核心目标: {summary.core_objective}")
        print(f"  关注点: {', '.join(summary.key_concerns)}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 对话摘要测试失败: {e}")
        return False

def test_performance_monitor():
    """测试性能监控器"""
    print("\n🧪 测试性能监控器...")
    
    try:
        from core.utils.performance_monitor import PerformanceMonitor
        
        # 创建性能监控器
        monitor = PerformanceMonitor(enable_logging=False)
        print("  ✅ 性能监控器创建成功")
        
        # 测试装饰器
        @monitor.monitor_function(context_size=100, token_count=50)
        def test_function():
            import time
            time.sleep(0.01)  # 模拟处理时间
            return "测试完成"
        
        result = test_function()
        print(f"  ✅ 装饰器测试成功: {result}")
        
        # 获取性能摘要
        summary = monitor.get_performance_summary()
        print(f"  📈 性能摘要: {summary}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 性能监控器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🤖 AI数据分析平台 - 连续追问功能简化测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 测试列表
    tests = [
        ("模块导入", test_imports),
        ("对话摘要", test_conversation_summary),
        ("独立上下文管理器", test_context_manager_without_streamlit),
        ("性能监控器", test_performance_monitor),
    ]
    
    results = []
    context_manager_class = None
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_name == "模块导入":
                success, context_manager_class = test_func()
            elif test_name == "上下文管理器基础" and context_manager_class:
                success = test_func(context_manager_class)
            else:
                success = test_func()
            
            results.append((test_name, success))
            status = "✅ 通过" if success else "❌ 失败"
            print(f"\n{test_name}: {status}")
            
        except Exception as e:
            print(f"\n❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 输出测试总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅" if success else "❌"
        print(f"  {status} {test_name}")
    
    print(f"\n通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！连续追问功能核心组件正常。")
        print("\n📋 下一步:")
        print("1. 安装可选依赖: pip install psutil")
        print("2. 启动应用: python streamlit_app.py")
        print("3. 测试完整功能")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 测试过程中发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
